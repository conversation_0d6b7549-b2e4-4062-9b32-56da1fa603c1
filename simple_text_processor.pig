-- =====================================================
-- Simple Apache Pig Script for Text Processing
-- =====================================================
-- This script processes text files to:
-- 1. Remove non-alphabetic characters
-- 2. Convert to lowercase  
-- 3. Sort by word length, then alphabetically
-- =====================================================

-- Define parameters
%default INPUT 'result.txt'
%default OUTPUT 'simple_processed_output'

-- Load data - assuming each line contains one word or word with count
raw_lines = LOAD '$INPUT' AS (line:chararray);

-- Split each line and take the first field (the word)
-- This handles both "word" and "word<TAB>count" formats
words_raw = FOREACH raw_lines GENERATE 
    TRIM(STRSPLIT(line, '\t')[0]) AS raw_word;

-- Clean the words: remove non-alphabetic characters and convert to lowercase
cleaned_words = FOREACH words_raw GENERATE 
    LOWER(REGEX_REPLACE(raw_word, '[^a-zA-Z]', '')) AS clean_word;

-- Filter out empty strings
valid_words = FILTER cleaned_words BY clean_word != '';

-- Remove duplicates
unique_words = DISTINCT valid_words;

-- Add word length for sorting
words_with_length = FOREACH unique_words GENERATE 
    clean_word,
    SIZE(clean_word) AS word_length;

-- Sort by length first, then alphabetically
-- Pattern: a, b, c, d, ..., z, aa, ab, ac, ..., az, ba, bb, ...
sorted_words = ORDER words_with_length BY word_length ASC, clean_word ASC;

-- Remove length field for final output
final_words = FOREACH sorted_words GENERATE clean_word;

-- Store results
STORE final_words INTO '$OUTPUT';

-- Debug: Show first 20 results
debug_sample = LIMIT final_words 20;
DUMP debug_sample;
