# Example: Before and After Processing

## Input (sample_result.txt):
```
the	150
and	120
word123	5
test-word	3
hello_world	2
data@processing	1
text#analysis	4
big$data	6
machine&learning	2
artificial*intelligence	1
natural+language	3
data=science	2
a	90
be	20
```

## After Processing (Expected Output):
```
a
be
and
big
the
data
text
word
test
hello
natural
machine
science
analysis
artificial
processing
helloworld
dataprocessing
textanalysis
bigdata
machinelearning
artificialintelligence
naturallanguage
datascience
```

## Explanation:

1. **Non-alphabetic characters removed:**
   - `word123` → `word`
   - `test-word` → `testword`
   - `hello_world` → `helloworld`
   - `data@processing` → `dataprocessing`
   - `text#analysis` → `textanalysis`
   - `big$data` → `bigdata`
   - `machine&learning` → `machinelearning`
   - `artificial*intelligence` → `artificialintelligence`
   - `natural+language` → `naturallanguage`
   - `data=science` → `datascience`

2. **Converted to lowercase:**
   - All words are now in lowercase

3. **Sorted by length, then alphabetically:**
   - Length 1: `a`
   - Length 2: `be`
   - Length 3: `and`, `big`, `the`
   - Length 4: `data`, `text`, `word`
   - Length 5: `hello`, `test`
   - And so on...

## Key Points:

- Words with the same length are sorted alphabetically
- Duplicates are removed (if any)
- Empty results from cleaning are filtered out
- Original word counts can be preserved if needed
