# Apache Pig Tutorial for Text Processing

## Overview
Apache Pig is a high-level platform for creating programs that run on Apache Hadoop. It provides a simple scripting language called Pig Latin that gets compiled into MapReduce jobs.

## Prerequisites
- Cloudera VM with Had<PERSON> and Pig installed
- Basic understanding of Hadoop ecosystem
- Your MapReduce result.txt file

## What is Apache Pig?

Apache Pig consists of:
- **Pig Latin**: A scripting language for data analysis
- **Pig Engine**: Converts Pig Latin scripts into MapReduce jobs
- **Execution Modes**: Local mode and MapReduce mode

## Key Concepts

### 1. Data Types
- **Scalar Types**: int, long, float, double, chararray, bytearray, boolean
- **Complex Types**: tuple, bag, map

### 2. Relations
- A relation is a bag (collection) of tuples
- Similar to a table in SQL

### 3. Basic Operations
- **LOAD**: Read data from files
- **STORE**: Write data to files
- **FILTER**: Select specific records
- **FOREACH**: Transform data
- **GROUP**: Group records by key
- **ORDER BY**: Sort data
- **DISTINCT**: Remove duplicates

## Your Text Processing Requirements

We need to:
1. Remove non-alphabetic characters
2. Convert to lowercase
3. Sort alphabetically by length, then alphabetically

## Step-by-Step Solution

### Step 1: Understanding Your Data Structure
Assuming your result.txt contains word counts like:
```
word1	count1
word2	count2
```

### Step 2: Basic Pig Script Structure
```pig
-- Load data
data = LOAD 'input_file' AS (field1:chararray, field2:int);

-- Process data
processed = FOREACH data GENERATE ...;

-- Store results
STORE processed INTO 'output_directory';
```

### Step 3: Text Cleaning Functions
Pig provides several built-in functions:
- **REGEX_EXTRACT**: Extract patterns
- **REGEX_REPLACE**: Replace patterns
- **LOWER**: Convert to lowercase
- **TRIM**: Remove whitespace

## Sample Pig Scripts

### Script 1: Basic Text Cleaning
```pig
-- Load your MapReduce result
raw_data = LOAD 'result.txt' AS (word:chararray, count:int);

-- Remove non-alphabetic characters and convert to lowercase
cleaned_data = FOREACH raw_data GENERATE 
    LOWER(REGEX_REPLACE(word, '[^a-zA-Z]', '')) AS clean_word,
    count;

-- Filter out empty strings
filtered_data = FILTER cleaned_data BY clean_word != '';

-- Store intermediate result
STORE filtered_data INTO 'cleaned_output';
```

### Script 2: Advanced Sorting
```pig
-- Load cleaned data
cleaned_data = LOAD 'cleaned_output' AS (clean_word:chararray, count:int);

-- Add word length for sorting
with_length = FOREACH cleaned_data GENERATE 
    clean_word,
    count,
    SIZE(clean_word) AS word_length;

-- Sort by length first, then alphabetically
sorted_data = ORDER with_length BY word_length ASC, clean_word ASC;

-- Remove the length field for final output
final_data = FOREACH sorted_data GENERATE clean_word, count;

-- Store final result
STORE final_data INTO 'final_sorted_output';
```

## Execution Instructions

### 1. Start Pig in Your Cloudera VM
```bash
# Start Pig in local mode (for testing)
pig -x local

# Start Pig in MapReduce mode (for production)
pig -x mapreduce
```

### 2. Run Pig Scripts
```bash
# Method 1: Interactive mode
pig -x local
grunt> exec 'your_script.pig';

# Method 2: Batch mode
pig -x local your_script.pig

# Method 3: With parameters
pig -x local -param input=result.txt -param output=processed_output your_script.pig
```

### 3. Check HDFS for Results
```bash
# List output directory
hdfs dfs -ls processed_output

# View results
hdfs dfs -cat processed_output/part-r-00000
```

## Advanced Features

### Using Parameters
```pig
-- Define parameters at the top
%default INPUT 'result.txt'
%default OUTPUT 'processed_output'

raw_data = LOAD '$INPUT' AS (word:chararray, count:int);
-- ... processing ...
STORE final_data INTO '$OUTPUT';
```

### Custom Functions (UDFs)
You can write custom Java functions for complex processing.

### Debugging Tips
```pig
-- Use DESCRIBE to check schema
DESCRIBE your_relation;

-- Use DUMP to see sample data (use sparingly with large datasets)
DUMP (LIMIT your_relation 10);

-- Use EXPLAIN to see execution plan
EXPLAIN your_relation;
```

## Common Pig Latin Functions for Text Processing

### String Functions
- `LOWER(string)` - Convert to lowercase
- `UPPER(string)` - Convert to uppercase
- `TRIM(string)` - Remove leading/trailing whitespace
- `SIZE(string)` - Get string length
- `SUBSTRING(string, start, end)` - Extract substring
- `REGEX_EXTRACT(string, pattern, index)` - Extract using regex
- `REGEX_REPLACE(string, pattern, replacement)` - Replace using regex

### Example Usage
```pig
processed = FOREACH raw_data GENERATE 
    TRIM(LOWER(REGEX_REPLACE(word, '[^a-zA-Z]', ''))) AS clean_word,
    count;
```

## Next Steps
1. Copy your result.txt to the appropriate location
2. Modify the sample scripts for your specific data format
3. Test with a small dataset first
4. Run the complete processing pipeline
5. Verify the output format matches your requirements

## Troubleshooting
- Check Hadoop logs if jobs fail
- Verify input file format and location
- Use LIMIT and DUMP for debugging small samples
- Check HDFS permissions
