-- =====================================================
-- Apache Pig Script for Text Processing
-- =====================================================
-- This script processes MapReduce output to:
-- 1. Remove non-alphabetic characters
-- 2. Convert to lowercase
-- 3. Sort by word length, then alphabetically
-- =====================================================

-- Define input and output parameters
%default INPUT 'result.txt'
%default OUTPUT 'processed_text_output'

-- Step 1: Load the MapReduce result file
-- Assuming format: word<TAB>count
raw_data = LOAD '$INPUT' AS (word:chararray, count:int);

-- Step 2: Clean the text data
-- Remove all non-alphabetic characters and convert to lowercase
cleaned_data = FOREACH raw_data GENERATE 
    LOWER(REGEX_REPLACE(word, '[^a-zA-Z]', '')) AS clean_word,
    count AS original_count;

-- Step 3: Filter out empty strings (words that had no alphabetic characters)
filtered_data = FILTER cleaned_data BY clean_word != '';

-- Step 4: Add word length for sorting purposes
with_length = FOREACH filtered_data GENERATE 
    clean_word,
    original_count,
    SIZE(clean_word) AS word_length;

-- Step 5: Sort by word length first (ascending), then alphabetically (ascending)
-- This gives us the pattern: a, b, c, ..., aa, ab, ac, ..., aaa, aab, etc.
sorted_data = ORDER with_length BY word_length ASC, clean_word ASC;

-- Step 6: Remove the word_length field for final output
final_data = FOREACH sorted_data GENERATE 
    clean_word,
    original_count;

-- Step 7: Store the processed results
STORE final_data INTO '$OUTPUT';

-- Optional: Store just the words without counts (if you only want the word list)
words_only = FOREACH final_data GENERATE clean_word;
STORE words_only INTO '${OUTPUT}_words_only';
