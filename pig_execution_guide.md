# Apache Pig Execution Guide

## Prerequisites Setup in Cloudera VM

### 1. Verify Pig Installation
```bash
# Check if Pig is installed
pig -version

# If not installed, install it
sudo yum install pig  # For CentOS/RHEL
# or
sudo apt-get install pig  # For Ubuntu/Debian
```

### 2. Start Hadoop Services
```bash
# Start Hadoop services (if not already running)
start-dfs.sh
start-yarn.sh

# Check if services are running
jps
```

## Step-by-Step Execution

### Step 1: Prepare Your Data

1. **Copy your MapReduce result.txt to HDFS:**
```bash
# Create input directory in HDFS
hdfs dfs -mkdir -p /user/input

# Copy your result.txt to HDFS
hdfs dfs -put result.txt /user/input/

# Verify the file is there
hdfs dfs -ls /user/input/
hdfs dfs -cat /user/input/result.txt | head -10
```

### Step 2: Test with Sample Data (Optional)
```bash
# Use the provided sample data for testing
hdfs dfs -put sample_result.txt /user/input/
```

### Step 3: Run Pig Scripts

#### Option A: Interactive Mode (Recommended for Learning)
```bash
# Start Pig in interactive mode
pig

# In the Pig shell (grunt>), run commands one by one:
grunt> raw_data = LOAD '/user/input/result.txt' AS (word:chararray, count:int);
grunt> DESCRIBE raw_data;
grunt> sample = LIMIT raw_data 5;
grunt> DUMP sample;

# Run the full processing script
grunt> exec 'text_processor.pig';
```

#### Option B: Batch Mode (Recommended for Production)
```bash
# Run the complete script
pig -param INPUT='/user/input/result.txt' -param OUTPUT='/user/output/processed' text_processor.pig

# Or run the simple version
pig -param INPUT='/user/input/result.txt' -param OUTPUT='/user/output/simple' simple_text_processor.pig
```

#### Option C: Local Mode (for Small Files)
```bash
# Run in local mode (processes files locally, not in HDFS)
pig -x local -param INPUT='result.txt' -param OUTPUT='local_output' simple_text_processor.pig
```

### Step 4: Check Results

```bash
# List output directory
hdfs dfs -ls /user/output/processed/

# View the results
hdfs dfs -cat /user/output/processed/part-r-00000

# Copy results back to local filesystem
hdfs dfs -get /user/output/processed/part-r-00000 final_processed_words.txt

# View locally
cat final_processed_words.txt
```

## Expected Output Format

Your processed text should look like this:
```
a
b
c
d
...
z
aa
ab
ac
ad
...
```

## Troubleshooting

### Common Issues and Solutions

1. **"Command not found: pig"**
   ```bash
   # Add Pig to PATH
   export PATH=$PATH:/usr/lib/pig/bin
   # Or find Pig installation
   find /usr -name "pig" -type f 2>/dev/null
   ```

2. **"Connection refused" errors**
   ```bash
   # Check Hadoop services
   jps
   # Restart if needed
   stop-all.sh
   start-all.sh
   ```

3. **Permission denied in HDFS**
   ```bash
   # Check HDFS permissions
   hdfs dfs -ls /user/
   # Create user directory if needed
   hdfs dfs -mkdir -p /user/$USER
   hdfs dfs -chown $USER:$USER /user/$USER
   ```

4. **Out of memory errors**
   ```bash
   # Increase heap size
   export JAVA_OPTS="-Xmx2g"
   pig -Dpig.additional.jars.uris=... your_script.pig
   ```

### Debug Commands

```bash
# Check Pig script syntax
pig -check text_processor.pig

# Dry run (explain execution plan)
pig -x local -e "
raw_data = LOAD 'sample_result.txt' AS (word:chararray, count:int);
EXPLAIN raw_data;
"

# View schema
pig -x local -e "
raw_data = LOAD 'sample_result.txt' AS (word:chararray, count:int);
DESCRIBE raw_data;
"
```

## Performance Tips

1. **Use appropriate data types** in LOAD statements
2. **Filter early** to reduce data size
3. **Use LIMIT** for testing with large datasets
4. **Consider partitioning** for very large datasets
5. **Monitor job progress** in Hadoop web UI (usually http://localhost:8088)

## Next Steps

1. Run the script with your actual result.txt
2. Verify the output format matches your requirements
3. Modify the script if needed for your specific data format
4. Consider creating additional scripts for other text processing tasks

## Alternative: One-liner for Quick Testing

```bash
# Quick test with local mode
pig -x local -e "
data = LOAD 'sample_result.txt' AS (word:chararray, count:int);
cleaned = FOREACH data GENERATE LOWER(REGEX_REPLACE(word, '[^a-zA-Z]', '')) AS clean_word;
filtered = FILTER cleaned BY clean_word != '';
unique = DISTINCT filtered;
with_len = FOREACH unique GENERATE clean_word, SIZE(clean_word) AS len;
sorted = ORDER with_len BY len ASC, clean_word ASC;
final = FOREACH sorted GENERATE clean_word;
STORE final INTO 'quick_output';
"
```
